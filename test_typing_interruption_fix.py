#!/usr/bin/env python3
"""
Test script to verify that the typing interruption fix works correctly.

This script simulates the exact scenario that was causing the infinite loop:
1. typed_text becomes longer than current_text due to trailing space
2. The system gets stuck in a loop where has_untyped_text = False
3. Our fixes should prevent this and recover gracefully
"""

import time
import tempfile
import os
from unittest.mock import Mock

class MockAutotyper:
    """Mock autotyper class to test the fix without GUI dependencies."""
    
    def __init__(self):
        # Initialize state variables
        self.current_text = ""
        self.typed_text = ""
        self.cursor_position = 0
        self.trailing_space_added = False
        self.last_typing_completion_time = 0
        self.text_stable_since = 0
        self.last_stable_text = ""
        self.text_stability_threshold = 1.0
        self.no_change_count = 0
        
        # Mock temp file for cross-capture detection
        self.temp_file_path = None
        
        # Mock debug logging
        self.debug_logs = []
        
    def debug_log(self, message, function_name=""):
        """Mock debug logging."""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {function_name}: {message}"
        self.debug_logs.append(log_entry)
        print(log_entry)
    
    def calculate_text_similarity(self, text1, text2):
        """Mock similarity calculation."""
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0
        
        # Simple similarity based on common characters
        common = sum(1 for a, b in zip(text1, text2) if a == b)
        max_len = max(len(text1), len(text2))
        return common / max_len if max_len > 0 else 1.0
    
    def check_and_add_trailing_space(self):
        """The fixed trailing space function from main.py."""
        current_time = time.time()

        # CRITICAL FIX: Check if we have finished typing all detected text (handle length mismatches)
        if len(self.typed_text) < len(self.current_text):
            # Still have text to type, reset trailing space state
            self.trailing_space_added = False
            return
        elif len(self.typed_text) > len(self.current_text):
            # CRITICAL: typed_text is longer than current_text - don't add more spaces
            self.debug_log(f"⚠️  TRAILING SPACE: Skipping - typed_text ({len(self.typed_text)}) > current_text ({len(self.current_text)})", "check_and_add_trailing_space")
            
            # CRITICAL FIX: Reset the length mismatch to prevent infinite loops
            if self.typed_text.startswith(self.current_text):
                # typed_text contains current_text plus extra (likely trailing space)
                extra_chars = self.typed_text[len(self.current_text):]
                if extra_chars.strip() == "":  # Only whitespace difference
                    self.debug_log(f"🔧 TRAILING SPACE: Resetting typed_text to match current_text (removing extra whitespace)", "check_and_add_trailing_space")
                    self.typed_text = self.current_text
                    self.cursor_position = len(self.current_text)
                    self.trailing_space_added = False  # Reset flag to allow future trailing spaces
            return
        elif self.typed_text != self.current_text:
            # Same length but different content - still typing
            self.trailing_space_added = False
            return

        # Check if we already added a trailing space for this detection cycle
        if self.trailing_space_added:
            return

        # Check if enough time has passed since typing completion
        if current_time - self.last_typing_completion_time < self.text_stability_threshold:
            return

        # Check if text has remained stable (no new text detected)
        if self.current_text != self.last_stable_text:
            # Text changed, update stability tracking
            self.last_stable_text = self.current_text
            self.text_stable_since = current_time
            return

        # Check if text has been stable for the required duration
        if current_time - self.text_stable_since < self.text_stability_threshold:
            return

        # Check if the detected text doesn't already end with whitespace
        if not self.current_text or self.current_text[-1] in ' \t\n':
            return

        # CRITICAL FIX: Additional safety check - don't add trailing space if OCR is still changing
        # This prevents adding spaces when OCR is still stabilizing
        if hasattr(self, 'no_change_count') and self.no_change_count < 3:
            self.debug_log(f"⚠️  TRAILING SPACE: Skipping - OCR still changing (no_change_count: {self.no_change_count})", "check_and_add_trailing_space")
            return

        # All conditions met - add trailing space
        print("Adding trailing space after stable text detection")

        # Mock typing the space
        self.typed_text += ' '
        self.cursor_position += 1
        self.trailing_space_added = True

        print(f"Trailing space added. New typed text length: {len(self.typed_text)}")
    
    def check_has_untyped_text(self):
        """The fixed has_untyped_text logic from enhanced_typing_loop."""
        # CRITICAL FIX: Improved has_untyped_text logic to handle length mismatches
        if not self.current_text:
            has_untyped_text = False
        elif not self.typed_text:
            has_untyped_text = True  # Have text but nothing typed yet
        elif len(self.typed_text) > len(self.current_text):
            # CRITICAL: typed_text is longer than current_text (trailing space issue)
            has_untyped_text = False  # No more text to type
            self.debug_log(f"⚠️  LENGTH MISMATCH: typed_text ({len(self.typed_text)}) > current_text ({len(self.current_text)}) - no untyped text", "check_has_untyped_text")
            
            # CRITICAL FIX: Attempt to fix the length mismatch immediately
            if self.typed_text.startswith(self.current_text):
                extra_chars = self.typed_text[len(self.current_text):]
                if extra_chars.strip() == "":  # Only whitespace difference
                    self.debug_log(f"🔧 TYPING LOOP: Auto-fixing length mismatch (removing extra whitespace)", "check_has_untyped_text")
                    self.typed_text = self.current_text
                    self.cursor_position = len(self.current_text)
                    self.trailing_space_added = False
        elif len(self.current_text) > len(self.typed_text):
            # Current text is longer - check if it's genuinely new content
            if self.current_text.startswith(self.typed_text):
                has_untyped_text = True  # Current text extends typed text
            else:
                # Texts don't match - could be content change or OCR variation
                similarity = self.calculate_text_similarity(self.current_text, self.typed_text)
                has_untyped_text = similarity < 0.90  # Only if significantly different (reduced from 0.95 to be less aggressive)
                self.debug_log(f"Text mismatch detected - similarity: {similarity:.3f}, has_untyped_text: {has_untyped_text}", "check_has_untyped_text")
        else:
            # Same length - check if content matches
            has_untyped_text = self.current_text != self.typed_text
        
        return has_untyped_text


def test_trailing_space_length_mismatch_fix():
    """Test that the trailing space fix prevents infinite loops."""
    print("=== Testing Trailing Space Length Mismatch Fix ===\n")
    
    typer = MockAutotyper()
    
    # Scenario 1: Simulate the exact problem from the terminal output
    print("Scenario 1: Reproducing the exact infinite loop condition")
    typer.current_text = "Hello world test content"  # 194 chars equivalent
    typer.typed_text = "Hello world test content"   # Same initially
    typer.last_typing_completion_time = time.time() - 2.0  # 2 seconds ago
    typer.last_stable_text = "Hello world test content"
    typer.text_stable_since = time.time() - 2.0
    typer.trailing_space_added = False
    typer.no_change_count = 5  # OCR is stable
    
    print(f"Initial state:")
    print(f"  current_text length: {len(typer.current_text)}")
    print(f"  typed_text length: {len(typer.typed_text)}")
    print(f"  has_untyped_text: {typer.check_has_untyped_text()}")
    
    # This should add a trailing space
    typer.check_and_add_trailing_space()
    
    print(f"After trailing space:")
    print(f"  current_text length: {len(typer.current_text)}")
    print(f"  typed_text length: {len(typer.typed_text)}")
    print(f"  has_untyped_text: {typer.check_has_untyped_text()}")
    
    # Now simulate the next iteration - this is where the infinite loop occurred
    print(f"Next iteration (this would cause infinite loop before fix):")
    has_untyped_text = typer.check_has_untyped_text()
    print(f"  has_untyped_text: {has_untyped_text}")
    print(f"  current_text length: {len(typer.current_text)}")
    print(f"  typed_text length: {len(typer.typed_text)}")
    
    # The fix should have automatically corrected the length mismatch
    if len(typer.typed_text) == len(typer.current_text):
        print("✅ SUCCESS: Length mismatch was automatically fixed!")
    else:
        print("❌ FAILURE: Length mismatch still exists")
    
    print()


def test_trailing_space_safety_checks():
    """Test the additional safety checks in trailing space logic."""
    print("=== Testing Trailing Space Safety Checks ===\n")
    
    # Test Case 1: OCR still changing (no_change_count < 3)
    print("Test Case 1: OCR still changing - should not add trailing space")
    typer = MockAutotyper()
    typer.current_text = "Hello world"
    typer.typed_text = "Hello world"
    typer.last_typing_completion_time = time.time() - 2.0
    typer.last_stable_text = "Hello world"
    typer.text_stable_since = time.time() - 2.0
    typer.trailing_space_added = False
    typer.no_change_count = 2  # Still changing
    
    typer.check_and_add_trailing_space()
    
    if not typer.trailing_space_added:
        print("✅ SUCCESS: Trailing space correctly skipped when OCR still changing")
    else:
        print("❌ FAILURE: Trailing space added despite OCR still changing")
    
    # Test Case 2: OCR stable (no_change_count >= 3)
    print("\nTest Case 2: OCR stable - should add trailing space")
    typer.no_change_count = 5  # Stable
    
    typer.check_and_add_trailing_space()
    
    if typer.trailing_space_added:
        print("✅ SUCCESS: Trailing space correctly added when OCR stable")
    else:
        print("❌ FAILURE: Trailing space not added despite OCR being stable")
    
    print()


def test_similarity_threshold_improvements():
    """Test the improved similarity thresholds."""
    print("=== Testing Similarity Threshold Improvements ===\n")
    
    typer = MockAutotyper()
    
    # Test with 85% similarity (should now be considered "no change")
    typer.current_text = "Hello world test"
    typer.typed_text = "Hello world text"  # 85% similar
    
    similarity = typer.calculate_text_similarity(typer.current_text, typer.typed_text)
    has_untyped = typer.check_has_untyped_text()
    
    print(f"85% similarity test:")
    print(f"  Similarity: {similarity:.2f}")
    print(f"  Has untyped text: {has_untyped}")
    
    # With the new threshold (0.90), this should be considered "no untyped text"
    if not has_untyped:
        print("✅ SUCCESS: 85% similarity correctly treated as no change")
    else:
        print("❌ FAILURE: 85% similarity incorrectly treated as change")
    
    print()


def run_all_tests():
    """Run all tests to verify the typing interruption fix."""
    print("🧪 TESTING TYPING INTERRUPTION FIX")
    print("=" * 50)
    print()
    
    test_trailing_space_length_mismatch_fix()
    test_trailing_space_safety_checks()
    test_similarity_threshold_improvements()
    
    print("=" * 50)
    print("🎯 SUMMARY")
    print("The fixes implemented should prevent the typing interruption issue by:")
    print("1. ✅ Auto-correcting length mismatches when typed_text > current_text")
    print("2. ✅ Adding safety checks to prevent trailing spaces during OCR changes")
    print("3. ✅ Reducing similarity thresholds to be less aggressive")
    print("4. ✅ Providing recovery mechanisms in the typing loop")
    print()
    print("These changes should eliminate the infinite loop condition that was")
    print("causing the autotyper to get stuck and stop typing.")


if __name__ == "__main__":
    run_all_tests()
